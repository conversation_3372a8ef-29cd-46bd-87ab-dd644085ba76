#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

#Depending on the operating system of the host machines(s) that will build or run the containers, the image specified in the FROM statement may need to be changed.
#For more information, please see https://aka.ms/containercompat

FROM hca-docker-innersource.repos.medcity.net/containers/base/dotnet-8.0-runtime:latest AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM hca-docker-innersource.repos.medcity.net/containers/base/dotnet-8.0-build:latest AS build
WORKDIR /src
COPY ["smart-cloud-order-type-api/smart-cloud-order-type-api.csproj", "smart-cloud-order-type-api/"]
COPY NuGet.config .

# COPY ["hcaroot.crt", "devroot.crt", "qaroot.crt", "/usr/local/share/ca-certificates/"]
# RUN update-ca-certificates

RUN dotnet restore "smart-cloud-order-type-api/smart-cloud-order-type-api.csproj"
COPY . .
WORKDIR "/src/smart-cloud-order-type-api"
RUN dotnet build "smart-cloud-order-type-api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "smart-cloud-order-type-api.csproj" -c Release -o /app/publish

# Do NOT modify the following line as it needed by CI/CD workflow
ARG IMAGE_VERSION

FROM base AS final
WORKDIR /app

# The following 4 labels are required.  In a multistage build
# they must be attached to the final image build stage as below.
LABEL company="HCA Healthcare"
LABEL organization="Healthtrust"
LABEL department="Supply Chain"
# Do NOT modify the following line as it needed by CI/CD workflow
LABEL version=$IMAGE_VERSION

COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "smart-cloud-order-type-api.dll"]
