<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Smart.Cloud.Core.Logging" Version="2.0.0" />
    <PackageReference Include="Smart.Cloud.Core.PubSub" Version="1.1.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\smart-cloud-order-type-api.Core\smart-cloud-order-type-api.Core.csproj" />
  </ItemGroup>

</Project>
