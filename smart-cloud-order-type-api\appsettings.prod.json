{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"Using": ["Serilog.Sinks.GoogleCloudLogging", "Serilog.Sinks.Console"], "MinimumLevel": "Information", "WriteTo": [{"Name": "GoogleCloudLogging", "Args": {"projectID": "hca-smr-dev", "restrictedToMinimumLevel": "Information", "outputTemplate": "{Timestamp:HH:mm:ss.fff zzz} [{Level}] {Message}{NewLine}{Exception}"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"standardErrorFromLevel": "Error", "outputTemplate": "{Timestamp:HH:mm:ss.fff zzz} [{Level}] {Message}{NewLine}{Exception}"}}]}}