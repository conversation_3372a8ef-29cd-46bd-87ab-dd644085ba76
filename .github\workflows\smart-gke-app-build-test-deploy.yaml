name: smart-gke-app-build-test-deploy

on: 
  push:
    branches: [ develop ]
  workflow_dispatch:

jobs:
  gke-app-build-test-deploy:
    uses: HCAHealthtrust/devops-github-actions/.github/workflows/smart-gke-app-build-qatest-deploy.yaml@main
    secrets:
       registry-username: ${{ secrets.HT_SMART_REGISTRY_USER }}
       registry-password: ${{ secrets.HT_SMART_REGISTRY_SECRET }}
       wiz-client-id: ${{ secrets.WIZ_CLIENT_ID }}
       wiz-client-secret: ${{ secrets.WIZ_CLIENT_SECRET }}
       ARGO_TOKEN: ${{ secrets.ARGO_TOKEN }}
    with:
      REGISTRY_URL: 'htpgsmartitemmanagement.repos.medcity.net'  # ex. htpgsmartitemmanagement.repos.medcity.net
      IMAGE_NAME: 'smart_cloud_order_type_api'  # This sets your image name inside the repo listed above
      run-qa-tests: false
      dockerfile-path: ./smart-cloud-order-type-api/Dockerfile  # Default path is the root directory
      
      ## These values only need to be filled out if  there is a QA workflow created to run tests
      #QA-repo: ''
      #QA-testing-workflow: ''
      #QA-repo-branch: ''

      #http_proxy:
      #https_proxy:
      #no_proxy:
