﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32616.157
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "smart-cloud-order-type-api", "smart-cloud-order-type-api\smart-cloud-order-type-api.csproj", "{8F92DAD1-A1FB-4099-B085-F6E7177EE582}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "smart-cloud-order-type-api.Core", "smart-cloud-order-type-api.Core\smart-cloud-order-type-api.Core.csproj", "{9732B95D-9D58-4E03-A4E4-A4B5E92597B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "smart-cloud-order-type-api.Infrastructure", "smart-cloud-order-type-api.Infrastructure\smart-cloud-order-type-api.Infrastructure.csproj", "{44B36271-D956-4F83-8991-B07FC37CBA65}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "smart-cloud-order-type-api.Services", "smart-cloud-order-type-api.Services\smart-cloud-order-type-api.Services.csproj", "{BB26F4C4-BBDD-468F-BE56-1BDFEE2E5921}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "smart-cloud-order-type-api.Tests", "smart-cloud-order-type-api.Tests\smart-cloud-order-type-api.Tests.csproj", "{7B2F67A0-016C-4D0D-BC38-E581D566AFB6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{785C9CCC-B99F-4708-8248-859EA08CBA42}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".github", ".github", "{BE47B641-91AF-4F44-93A8-3E9DFDEE2FA5}"
	ProjectSection(SolutionItems) = preProject
		.github\rename_solution.sh = .github\rename_solution.sh
		.github\template.yml = .github\template.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "workflows", "workflows", "{3C375A87-6133-4CA3-B14C-95E6AF4E6738}"
	ProjectSection(SolutionItems) = preProject
		dotnet.yml = dotnet.yml
		.github\workflows\rename_solution.yml = .github\workflows\rename_solution.yml
		.github\workflows\smart-gke-app-build-test-deploy.yaml = .github\workflows\smart-gke-app-build-test-deploy.yaml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8F92DAD1-A1FB-4099-B085-F6E7177EE582}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F92DAD1-A1FB-4099-B085-F6E7177EE582}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F92DAD1-A1FB-4099-B085-F6E7177EE582}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F92DAD1-A1FB-4099-B085-F6E7177EE582}.Release|Any CPU.Build.0 = Release|Any CPU
		{9732B95D-9D58-4E03-A4E4-A4B5E92597B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9732B95D-9D58-4E03-A4E4-A4B5E92597B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9732B95D-9D58-4E03-A4E4-A4B5E92597B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9732B95D-9D58-4E03-A4E4-A4B5E92597B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{44B36271-D956-4F83-8991-B07FC37CBA65}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44B36271-D956-4F83-8991-B07FC37CBA65}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44B36271-D956-4F83-8991-B07FC37CBA65}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44B36271-D956-4F83-8991-B07FC37CBA65}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB26F4C4-BBDD-468F-BE56-1BDFEE2E5921}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB26F4C4-BBDD-468F-BE56-1BDFEE2E5921}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB26F4C4-BBDD-468F-BE56-1BDFEE2E5921}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB26F4C4-BBDD-468F-BE56-1BDFEE2E5921}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B2F67A0-016C-4D0D-BC38-E581D566AFB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B2F67A0-016C-4D0D-BC38-E581D566AFB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B2F67A0-016C-4D0D-BC38-E581D566AFB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B2F67A0-016C-4D0D-BC38-E581D566AFB6}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{BE47B641-91AF-4F44-93A8-3E9DFDEE2FA5} = {785C9CCC-B99F-4708-8248-859EA08CBA42}
		{3C375A87-6133-4CA3-B14C-95E6AF4E6738} = {BE47B641-91AF-4F44-93A8-3E9DFDEE2FA5}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {051D8667-1D20-4636-B2AC-9223AAD88FA7}
	EndGlobalSection
EndGlobal
