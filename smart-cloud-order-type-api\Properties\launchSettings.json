{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:44597", "sslPort": 44366}}, "profiles": {"smart-cloud-order-type-api": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DatabaseConnectionString": "Data Source=localhost\\sqlexpress;Initial Catalog=rules_engine;Integrated Security=True;", "GCP_PROJECT_ID": "hca-smr-dev"}, "applicationUrl": "https://localhost:7202;http://localhost:5202", "dotnetRunMessages": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "publishAllPorts": true, "useSSL": true}}}