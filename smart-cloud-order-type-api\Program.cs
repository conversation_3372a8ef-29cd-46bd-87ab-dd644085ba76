using Microsoft.OpenApi.Models;
using smart-cloud-order-type-api.Core.Interfaces.IRepositories.IConfiguration;
using smart-cloud-order-type-api.Infrastructure.Repositories.Configuration;
using Smart.Cloud.Core.Api.Middleware;
using Smart.Cloud.Core.Logging;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddSingleton<IConnectionFactory, ConnectionFactory>();

AddSwaggerServicesToAppBuilder(builder);
builder.AddStructuredLoggerToCloudApiService();

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();
// Setting up app HTTP request pipeline
AddSwaggerToApp(app);
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthorization();
app.UseMiddleware<ErrorHandlingMiddleware>();
app.MapControllers();

app.Run();
void AddSwaggerServicesToAppBuilder(WebApplicationBuilder appBuilder)
{
    appBuilder.Services.AddEndpointsApiExplorer();
    appBuilder.Services.AddSwaggerGen(
        c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "smart-cloud-order-type-api", Version = "v1" });
            c.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
            c.CustomOperationIds(e => $"{e.ActionDescriptor.RouteValues["action"]}-{e.ActionDescriptor.RouteValues["controller"]}");
        });
}

void AddSwaggerToApp(WebApplication webApp)
{
    // Swagger is available in production, not just development.
    webApp.UseSwagger();
    webApp.UseSwaggerUI(c =>
        {
            c.RoutePrefix = "swagger";
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "smart-cloud-order-type-api");
        }
    );
}