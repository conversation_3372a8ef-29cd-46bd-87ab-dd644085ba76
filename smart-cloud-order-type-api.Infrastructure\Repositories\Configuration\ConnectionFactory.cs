﻿using Dapper;
using smart-cloud-order-type-api.Core.Interfaces.IRepositories.IConfiguration;
using System.Data;
using System.Data.SqlClient;

namespace smart-cloud-order-type-api.Infrastructure.Repositories.Configuration
{
    public class ConnectionFactory : IConnectionFactory
    {
        string ConnectionString { get; set; }

        public IDbConnection GetConnection()
        {
            ConnectionString = Environment.GetEnvironmentVariable("DatabaseConnectionString");
            DefaultTypeMap.MatchNamesWithUnderscores = true;

#if DEBUG
            var localDatabaseEnvKey = Environment.GetEnvironmentVariable("LOCAL_DATABASE_ENV_KEY");
            if (!string.IsNullOrEmpty(localDatabaseEnvKey))
            {
                var server = Environment.GetEnvironmentVariable(localDatabaseEnvKey, EnvironmentVariableTarget.User);
                if (!string.IsNullOrEmpty(server))
                {
                    var conn = new SqlConnectionStringBuilder(ConnectionString) { DataSource = server };
                    ConnectionString = conn.ConnectionString;
                }
            }
#endif

            return new SqlConnection(ConnectionString);
        }

        public void ValidateOpenConnection(IDbConnection connection)
        {
            if (connection == null)
            {
                throw new InvalidOperationException("Cannot open a null connection.");
            }
            if (string.IsNullOrEmpty(connection.ConnectionString) || string.IsNullOrEmpty(connection.Database))
            {
                throw new InvalidOperationException("Cannot open a connection without specifying a connection string or database.");
            }
            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }
        }
    }
}
